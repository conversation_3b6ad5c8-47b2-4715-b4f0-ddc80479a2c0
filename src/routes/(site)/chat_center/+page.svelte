<!-- +page.svelte -->
<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import PlatformIdentityList from '$lib/components/chat/PlatformIdentityList.svelte';
	import ConversationView from '$lib/components/conversation/ConversationView.svelte';
	import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { CustomerPlatformIdentity, Customer } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	import { t } from '$lib/stores/i18n';

	export let data;

	let selectedPlatformId: number | null = null;
	let selectedCustomerId: number | null = null;
	let selectedCustomer: Customer | null = null;
	let selectedPlatformIdentity: CustomerPlatformIdentity | null = null;
	let latestTicketId: number | null = null;
	let loading = false;

	// Initialize with data from server
	let platformIdentities: CustomerPlatformIdentity[] = data.platformIdentities || [];
	let total = data.total || 0;
	let currentPage = data.page || 1;
	let hasMore = data.hasMore || false;

	onMount(() => {
		// Connect WebSocket for real-time updates
		platformWebSocket.connect();
	});

	onDestroy(() => {
		platformWebSocket.disconnect();
	});

	async function handlePlatformSelect(
		event: CustomEvent<{
			platformId: number;
			customerId: number;
			platformIdentity: CustomerPlatformIdentity;
			isAutoUpdate?: boolean;
		}>
	) {
		const { platformId, customerId, platformIdentity, isAutoUpdate = false } = event.detail;

		// Validate that we have both values
		if (!platformId || !customerId) {
			console.error('Missing platformId or customerId:', { platformId, customerId });
			return;
		}

		// Store the complete platform identity object
		selectedPlatformIdentity = platformIdentity;

		// Extract the latest ticket ID if available (using bracket notation for runtime properties)
		const newLatestTicketId = (platformIdentity as any)['latest_ticket_id'] || null;

		// Log ticket ID changes for debugging
		// if (newLatestTicketId !== latestTicketId) {
		// 	console.log('chat_center/+page.svelte: latestTicketId changed from', latestTicketId, 'to', newLatestTicketId, isAutoUpdate ? '(auto-update)' : '(manual selection)');
		// }

		latestTicketId = newLatestTicketId;

		// Update selected platform ID - this will trigger ConversationView to reload
		selectedPlatformId = platformId;
		selectedCustomerId = customerId;

		// Load customer details if not already loaded or if different customer
		if (!selectedCustomer || selectedCustomer.customer_id !== customerId) {
			await loadCustomerDetails(customerId);
		}
	}

	async function loadCustomerDetails(customerId: number) {
		try {
			loading = true;
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`, {
				credentials: 'include'
			});

			if (response.ok) {
				selectedCustomer = await response.json();
			}
			// console.log('loadCustomerDetails: ', selectedCustomer);
		} catch (error) {
			console.error('Error loading customer:', error);
		} finally {
			loading = false;
		}
	}

	async function handleLoadMore(event: CustomEvent) {
		// Load more platform identities
		currentPage++;
		const response = await fetch(
			`${getBackendUrl()}/customer/api/platform-identities/?page=${currentPage}`,
			{ credentials: 'include' }
		);

		if (response.ok) {
			const data = await response.json();
			platformIdentities = [...platformIdentities, ...data.results];
			hasMore = !!data.next;
		}
	}

	/**
	 * Handle automatic platform updates from PlatformIdentityList polling
	 */
	async function handlePlatformUpdate(event: CustomEvent) {
		const {
			platformId,
			customerId,
			platformIdentity,
			changeType,
			previousTicketId,
			currentTicketId,
			isAutoUpdate
		} = event.detail;

		// console.log('chat_center/+page.svelte: Received automatic platform update:', {
		// 	platformId,
		// 	changeType,
		// 	previousTicketId,
		// 	currentTicketId,
		// 	isAutoUpdate
		// });

		// Use the existing handlePlatformSelect logic for consistency
		await handlePlatformSelect({
			detail: {
				platformId,
				customerId,
				platformIdentity,
				isAutoUpdate: true
			}
		} as CustomEvent);
	}

	/**
	 * Handle general platform data changes notification
	 */
	function handlePlatformDataChanged(event: CustomEvent) {
		const { changedPlatforms, timestamp } = event.detail;

		// console.log('chat_center/+page.svelte: Platform data changed:', {
		// 	changedPlatformsCount: changedPlatforms.length,
		// 	timestamp,
		// 	changedPlatforms
		// });

		// This is just for logging/monitoring purposes
		// The actual updates are handled by handlePlatformUpdate
	}

	// Reactive debugging for data flow verification
	// $: if (latestTicketId) {
	// 	console.log('chat_center/+page.svelte: Reactive - latestTicketId updated:', latestTicketId);
	// }

	// $: if (selectedPlatformId && selectedCustomer) {
	// 	console.log('chat_center/+page.svelte: Reactive - Ready to pass data to ConversationView:', {
	// 		customerId: selectedCustomer.customer_id,
	// 		platformId: selectedPlatformId,
	// 		ticketId: latestTicketId
	// 	});
	// }

	// Compute latest ticket owner ID from platform identity
	$: latestTicketOwnerId = selectedPlatformIdentity ? (selectedPlatformIdentity as any)['latest_ticket_owner_id'] || 0 : 0;

	// Reference to PlatformIdentityList component
	let platformIdentityListRef: any;

	/**
	 * Handle messages marked as read event from ConversationView
	 */
	function handleMessagesMarkedAsRead(event: CustomEvent) {
		const { platformId, messageCount } = event.detail;

		// Update unread count in PlatformIdentityList
		if (platformIdentityListRef && platformIdentityListRef.updateUnreadCount) {
			platformIdentityListRef.updateUnreadCount(platformId, -messageCount);
		}
	}
</script>

<div class="flex h-screen bg-gray-100">
	<!-- Left Panel: All Platform Identities -->
	<div class="flex w-1/4 min-w-[400px] flex-col border-r border-gray-200 bg-white">
		<PlatformIdentityList
			bind:this={platformIdentityListRef}
			{platformIdentities}
			{selectedPlatformId}
			{hasMore}
			access_token={data.access_token}
			currentUserFullName={data.fullname}
			on:select={handlePlatformSelect}
			on:loadMore={handleLoadMore}
			on:platformUpdate={handlePlatformUpdate}
			on:platformDataChanged={handlePlatformDataChanged}
		/>
	</div>

	<!-- Middle Panel: Conversation -->
	<div class="flex flex-1 flex-col bg-white">
		{#if selectedPlatformId && selectedCustomer}
			<ConversationView
				customerId={selectedCustomerId}
				platformId={selectedPlatformId}
				ticketId={latestTicketId}
				users={data.allUsers}
				priorities={data.allPriorities}
				statuses={data.allStatuses}
				topics={data.allTopics}
				access_token={data.access_token}
				on:messagesMarkedAsRead={handleMessagesMarkedAsRead}
			/>
		{:else}
			<div class="flex flex-1 items-center justify-center text-gray-500">
				<div class="text-center">
					<svg
						class="mx-auto mb-4 h-12 w-12 text-gray-400"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
						/>
					</svg>
					<p class="text-lg font-medium">{t('select_conversation')}</p>
					<p class="mt-1 text-sm text-gray-500">{t('choose_platform_identity')}</p>
				</div>
			</div>
		{/if}
	</div>

	<!-- Right Panel: Customer Info -->
	<!-- <div class="w-84 bg-white border-l border-gray-200"> -->
	<div class="flex w-1/4 min-w-[400px] flex-col overflow-hidden border-l border-gray-200 bg-white">
		{#if selectedPlatformId && selectedCustomer}
			<CustomerInfoPanel
				customer={selectedCustomer}
				platformId={selectedPlatformId}
				access_token={data.access_token}
				ticketId={latestTicketId}
			/>
		{:else}
			<div class="p-6 text-center text-gray-500">
				<p>{t('select_conversation_view_details')}</p>
			</div>
		{/if}
	</div>
</div>

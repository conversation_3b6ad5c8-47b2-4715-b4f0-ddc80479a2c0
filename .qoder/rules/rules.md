---
trigger: always_on
alwaysApply: true
---

- Use the Systematic Sequential Thinking MCP to methodically analyze requirements, plan system architecture, identify dependencies, and outline implementation steps in a clear, logical sequence.
- Treat the Context7 MCP as the definitive reference source - consult it consistently for specifications, constraints, design patterns, and implementation guidelines at every stage of development.
- Do not create Playwright test or run any test after implementation without my clear instructions